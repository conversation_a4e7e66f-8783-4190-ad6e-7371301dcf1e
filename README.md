# 入学待办事项系统

学生入学待办事项管理系统，包含H5移动端和网页管理后台。

## 项目结构

```
NewStudentEnrollment/
├── backend/          # FastAPI后端
├── frontend/         # 前端项目
│   ├── h5-mobile/   # H5移动端
│   └── admin-web/   # 管理后台
├── database/         # 数据库相关
└── docs/            # 项目文档
```

## 技术栈

### 后端
- FastAPI (Python 3.8+)
- aiomysql + SQLAlchemy
- JWT认证
- 自动API文档

### 前端
- H5移动端：Vue 3 + Vant UI + PWA
- 管理后台：Vue 3 + Element Plus
- 国际化：Vue I18n
- 状态管理：Pinia

### 数据库
- MySQL 8.0+
- 双数据库架构（smartcampus + 本地待办系统）

## 快速开始

### 1. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE school_enrollment_todo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p school_enrollment_todo < database/schema.sql
mysql -u root -p school_enrollment_todo < database/init_data.sql
```

### 2. 后端启动
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# 编辑 .env 文件配置数据库连接
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 前端启动
```bash
# H5移动端
cd frontend/h5-mobile
npm install
npm run dev

# 管理后台
cd frontend/admin-web
npm install
npm run dev
```

## 访问地址

- H5移动端：http://localhost:3000
- 管理后台：http://localhost:3001
- API文档：http://localhost:8000/docs

## 功能特性

- 学号登录（整合smartcampus数据库）
- 中英文双语支持
- 待办事项管理
- 文件上传
- 进度跟踪
- PWA支持
- 响应式设计

## 开发说明

详细的开发文档请查看 `docs/` 目录。
