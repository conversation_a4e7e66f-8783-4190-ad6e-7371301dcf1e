# 学生信息数据库配置（smartcampus）
STUDENT_DB_HOST=**********
STUDENT_DB_USER=ethanchen
STUDENT_DB_PASSWORD=Spal#2@25
STUDENT_DB_NAME=smartcampus

# 本地待办系统数据库配置
TODO_DB_HOST=localhost
TODO_DB_USER=root
TODO_DB_PASSWORD=your_password
TODO_DB_NAME=school_enrollment_todo

# JWT配置
SECRET_KEY=your-very-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
DEBUG=True
HOST=0.0.0.0
PORT=8000

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
