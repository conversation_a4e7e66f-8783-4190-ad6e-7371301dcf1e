from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from datetime import timedelta
from typing import Dict, Any

from app.schemas.auth import StudentLoginRequest, AdminLoginRequest, LoginResponse, UserInfo
from app.core.security import create_access_token, verify_token
from app.database import db_manager
from app.config import settings

router = APIRouter()
security = HTTPBearer()

@router.post("/student-login", response_model=LoginResponse)
async def student_login(request: StudentLoginRequest):
    """
    学生登录接口
    通过学号验证，整合smartcampus数据库
    """
    try:
        # 1. 从smartcampus查询学生信息
        student_info = await db_manager.get_student_info(request.studentId)
        if not student_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学号不存在或无效" if request.language == "zh-cn" else "Student ID not found or invalid"
            )
        
        # 2. 创建或更新本地用户记录
        local_user = await db_manager.create_or_update_local_user(student_info, request.language)
        if not local_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户信息创建失败" if request.language == "zh-cn" else "Failed to create user information"
            )
        
        # 3. 生成JWT token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": str(local_user["id"]),
                "student_id": request.studentId,
                "role": "student",
                "language": request.language
            },
            expires_delta=access_token_expires
        )
        
        # 4. 返回结果
        user_info = UserInfo(
            id=local_user["id"],
            studentId=request.studentId,
            name=student_info["stucName"],
            realName=student_info["stucName"],
            language=request.language,
            role="student",
            avatarUrl=local_user.get("avatar_url")
        )
        
        return LoginResponse(
            success=True,
            token=access_token,
            user=user_info,
            message="登录成功" if request.language == "zh-cn" else "Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Student login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试" if request.language == "zh-cn" else "Login failed, please try again later"
        )

@router.post("/admin-login", response_model=LoginResponse)
async def admin_login(request: AdminLoginRequest):
    """
    管理员登录接口
    使用固定的管理员账号密码
    """
    try:
        # 简单的管理员验证（实际项目中应该从数据库验证）
        if request.username != "admin" or request.password != "admin123":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误" if request.language == "zh-cn" else "Invalid username or password"
            )
        
        # 生成JWT token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": "admin",
                "username": request.username,
                "role": "admin",
                "language": request.language
            },
            expires_delta=access_token_expires
        )
        
        user_info = UserInfo(
            id=0,
            username=request.username,
            name="系统管理员" if request.language == "zh-cn" else "System Administrator",
            realName="系统管理员" if request.language == "zh-cn" else "System Administrator",
            language=request.language,
            role="admin"
        )
        
        return LoginResponse(
            success=True,
            token=access_token,
            user=user_info,
            message="登录成功" if request.language == "zh-cn" else "Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Admin login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试" if request.language == "zh-cn" else "Login failed, please try again later"
        )

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """获取当前用户信息"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload.get("role") == "student":
        user_id = int(payload.get("sub"))
        user = await db_manager.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        return user
    else:
        # 管理员用户
        return {
            "id": 0,
            "username": payload.get("username"),
            "role": "admin",
            "language": payload.get("language", "zh-cn")
        }

@router.get("/me")
async def get_me(current_user: Dict[str, Any] = Depends(get_current_user)):
    """获取当前用户信息"""
    return {"user": current_user}
