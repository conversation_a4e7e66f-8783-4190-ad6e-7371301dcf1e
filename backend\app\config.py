from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # 学生信息数据库配置（smartcampus）
    STUDENT_DB_HOST: str = "**********"
    STUDENT_DB_USER: str = "ethanchen"
    STUDENT_DB_PASSWORD: str = "Spal#2@25"
    STUDENT_DB_NAME: str = "smartcampus"
    
    # 本地待办系统数据库配置
    TODO_DB_HOST: str = "localhost"
    TODO_DB_USER: str = "root"
    TODO_DB_PASSWORD: str = "your_password"
    TODO_DB_NAME: str = "school_enrollment_todo"
    
    # JWT配置
    SECRET_KEY: str = "your-very-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 应用配置
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10485760  # 10MB
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001"
    ]
    
    @property
    def student_database_url(self) -> str:
        return f"mysql+aiomysql://{self.STUDENT_DB_USER}:{self.STUDENT_DB_PASSWORD}@{self.STUDENT_DB_HOST}/{self.STUDENT_DB_NAME}"
    
    @property
    def todo_database_url(self) -> str:
        return f"mysql+aiomysql://{self.TODO_DB_USER}:{self.TODO_DB_PASSWORD}@{self.TODO_DB_HOST}/{self.TODO_DB_NAME}"
    
    class Config:
        env_file = ".env"

# 创建全局设置实例
settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
