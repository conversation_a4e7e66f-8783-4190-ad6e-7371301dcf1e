from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from typing import Optional, Dict, Any
import aiomysql
from app.config import settings

class DatabaseManager:
    def __init__(self):
        # 创建异步引擎
        self.student_engine = create_async_engine(
            settings.student_database_url, 
            echo=settings.DEBUG,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        self.todo_engine = create_async_engine(
            settings.todo_database_url, 
            echo=settings.DEBUG,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # 创建会话工厂
        self.StudentSession = sessionmaker(
            self.student_engine, class_=AsyncSession, expire_on_commit=False
        )
        self.TodoSession = sessionmaker(
            self.todo_engine, class_=AsyncSession, expire_on_commit=False
        )
    
    async def get_student_info(self, student_id: str) -> Optional[Dict[str, Any]]:
        """从smartcampus数据库查询学生信息"""
        try:
            async with self.StudentSession() as session:
                result = await session.execute(
                    text("SELECT stucName, stuid FROM stuinfosp WHERE stuid = :student_id"),
                    {"student_id": student_id}
                )
                row = result.fetchone()
                if row:
                    return {"stucName": row[0], "stuid": row[1]}
                return None
        except Exception as e:
            print(f"Error querying student info: {e}")
            return None
    
    async def create_or_update_local_user(self, student_info: Dict[str, Any], language: str = "zh-cn") -> Optional[Dict[str, Any]]:
        """创建或更新本地用户"""
        try:
            async with self.TodoSession() as session:
                # 检查用户是否存在
                result = await session.execute(
                    text("SELECT * FROM users WHERE student_id = :student_id"),
                    {"student_id": student_info["stuid"]}
                )
                existing_user = result.fetchone()
                
                if not existing_user:
                    # 创建新用户
                    await session.execute(
                        text("""INSERT INTO users (student_id, real_name, nickname, role, language, last_login_time, created_at) 
                               VALUES (:student_id, :real_name, :nickname, :role, :language, NOW(), NOW())"""),
                        {
                            "student_id": student_info["stuid"],
                            "real_name": student_info["stucName"],
                            "nickname": student_info["stucName"],
                            "role": "student",
                            "language": language
                        }
                    )
                else:
                    # 更新用户信息
                    await session.execute(
                        text("""UPDATE users SET real_name = :real_name, nickname = :nickname, 
                               language = :language, last_login_time = NOW(), last_sync_time = NOW() 
                               WHERE student_id = :student_id"""),
                        {
                            "real_name": student_info["stucName"],
                            "nickname": student_info["stucName"],
                            "language": language,
                            "student_id": student_info["stuid"]
                        }
                    )
                
                await session.commit()
                
                # 返回用户信息
                result = await session.execute(
                    text("SELECT * FROM users WHERE student_id = :student_id"),
                    {"student_id": student_info["stuid"]}
                )
                user = result.fetchone()
                if user:
                    return {
                        "id": user[0],
                        "student_id": user[1],
                        "real_name": user[2],
                        "nickname": user[3],
                        "role": user[4],
                        "language": user[5],
                        "avatar_url": user[6],
                        "is_active": user[7],
                        "last_login_time": user[8],
                        "last_sync_time": user[9],
                        "created_at": user[10],
                        "updated_at": user[11]
                    }
                return None
        except Exception as e:
            print(f"Error creating/updating local user: {e}")
            return None
    
    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据用户ID获取用户信息"""
        try:
            async with self.TodoSession() as session:
                result = await session.execute(
                    text("SELECT * FROM users WHERE id = :user_id"),
                    {"user_id": user_id}
                )
                user = result.fetchone()
                if user:
                    return {
                        "id": user[0],
                        "student_id": user[1],
                        "real_name": user[2],
                        "nickname": user[3],
                        "role": user[4],
                        "language": user[5],
                        "avatar_url": user[6],
                        "is_active": user[7],
                        "last_login_time": user[8],
                        "last_sync_time": user[9],
                        "created_at": user[10],
                        "updated_at": user[11]
                    }
                return None
        except Exception as e:
            print(f"Error getting user by id: {e}")
            return None

# 全局数据库管理器实例
db_manager = DatabaseManager()
