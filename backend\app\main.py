from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import os

from app.config import settings
from app.api import auth

# 创建FastAPI应用
app = FastAPI(
    title="入学待办事项系统API",
    description="学生入学待办事项管理系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    print("=" * 50)
    print("🚀 FastAPI应用启动成功")
    print(f"📖 API文档地址: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"📚 ReDoc文档: http://{settings.HOST}:{settings.PORT}/redoc")
    print(f"🌍 CORS允许的源: {settings.ALLOWED_ORIGINS}")
    print(f"📁 上传目录: {settings.UPLOAD_DIR}")
    print("=" * 50)

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    print("👋 FastAPI应用关闭")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "入学待办事项系统API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
