from pydantic import BaseModel, Field
from typing import Optional

class StudentLoginRequest(BaseModel):
    """学生登录请求"""
    studentId: str = Field(..., min_length=1, max_length=20, description="学号")
    language: str = Field(default="zh-cn", description="语言偏好")

class AdminLoginRequest(BaseModel):
    """管理员登录请求"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, description="密码")
    language: str = Field(default="zh-cn", description="语言偏好")

class UserInfo(BaseModel):
    """用户信息"""
    id: int
    studentId: Optional[str] = None
    username: Optional[str] = None
    name: str
    realName: str
    language: str
    role: str
    avatarUrl: Optional[str] = None

class LoginResponse(BaseModel):
    """登录响应"""
    success: bool
    token: str
    user: UserInfo
    message: Optional[str] = None

class TokenData(BaseModel):
    """令牌数据"""
    sub: str
    student_id: Optional[str] = None
    username: Optional[str] = None
    role: str
    language: str
    exp: int
