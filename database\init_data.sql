-- 初始数据插入

-- 插入管理员用户
INSERT INTO users (student_id, real_name, nickname, role, language) VALUES
('admin', '系统管理员', '管理员', 'admin', 'zh-cn');

-- 插入基础待办模板
INSERT INTO todo_templates (title, title_en, description, description_en, category, priority, instructions, instructions_en, created_by, sort_order) VALUES
('学籍注册', 'Student Registration', 
 '完成学籍信息注册和确认，包括个人信息核对、专业确认等重要步骤。', 
 'Complete student registration and confirmation, including personal information verification and major confirmation.',
 '学籍', 'high', 
 '1. 登录学籍管理系统\n2. 核对个人基本信息\n3. 确认专业和班级信息\n4. 上传证件照片\n5. 提交注册申请',
 '1. Log in to the student registration system\n2. Verify personal information\n3. Confirm major and class information\n4. Upload ID photo\n5. Submit registration application',
 1, 1),

('宿舍申请', 'Dormitory Application',
 '申请学生宿舍住宿，选择宿舍类型和室友偏好。',
 'Apply for student dormitory accommodation, select dormitory type and roommate preferences.',
 '住宿', 'medium',
 '1. 查看宿舍类型和费用\n2. 选择宿舍楼和房间类型\n3. 填写室友偏好\n4. 上传健康证明\n5. 缴纳住宿费用',
 '1. View dormitory types and fees\n2. Select dormitory building and room type\n3. Fill in roommate preferences\n4. Upload health certificate\n5. Pay accommodation fees',
 1, 2),

('学费缴纳', 'Tuition Payment',
 '缴纳学期学费和相关费用，确保按时完成缴费。',
 'Pay semester tuition and related fees, ensure timely payment completion.',
 '财务', 'high',
 '1. 查看缴费通知单\n2. 确认缴费金额和项目\n3. 选择缴费方式\n4. 完成在线缴费\n5. 保存缴费凭证',
 '1. View payment notice\n2. Confirm payment amount and items\n3. Select payment method\n4. Complete online payment\n5. Save payment receipt',
 1, 3),

('体检报告', 'Medical Examination',
 '提交入学体检报告，确保符合入学健康要求。',
 'Submit entrance medical examination report to ensure compliance with health requirements.',
 '健康', 'medium',
 '1. 前往指定医院体检\n2. 完成所有体检项目\n3. 获取体检报告\n4. 上传体检报告扫描件\n5. 等待审核结果',
 '1. Go to designated hospital for examination\n2. Complete all examination items\n3. Obtain examination report\n4. Upload scanned examination report\n5. Wait for review results',
 1, 4),

('课程选择', 'Course Selection',
 '选择本学期的课程，包括必修课和选修课。',
 'Select courses for this semester, including required and elective courses.',
 '学习', 'medium',
 '1. 查看课程目录\n2. 了解课程要求和时间\n3. 选择必修课程\n4. 选择感兴趣的选修课\n5. 确认课程表',
 '1. View course catalog\n2. Understand course requirements and schedule\n3. Select required courses\n4. Choose elective courses of interest\n5. Confirm course schedule',
 1, 5);

-- 插入系统配置
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public, updated_by) VALUES
('system_name', '入学待办事项系统', 'string', '系统名称', TRUE, 1),
('system_name_en', 'Student Enrollment Todo System', 'string', '系统英文名称', TRUE, 1),
('default_language', 'zh-cn', 'string', '默认语言', TRUE, 1),
('file_upload_max_size', '10485760', 'number', '文件上传最大大小（字节）', FALSE, 1),
('supported_languages', '["zh-cn", "en-us"]', 'json', '支持的语言列表', TRUE, 1),
('contact_email', '<EMAIL>', 'string', '联系邮箱', TRUE, 1),
('help_url', 'https://help.school.edu', 'string', '帮助文档链接', TRUE, 1);
