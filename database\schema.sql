-- 入学待办事项系统数据库结构
-- 数据库名称：school_enrollment_todo

-- 1. 用户表
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
  real_name VARCHAR(100) NOT NULL COMMENT '真实姓名（来自smartcampus）',
  nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  role ENUM('student', 'admin') DEFAULT 'student' COMMENT '用户角色',
  language VARCHAR(10) DEFAULT 'zh-cn' COMMENT '语言偏好',
  avatar_url VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
  last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后同步时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_student_id (student_id),
  INDEX idx_role (role),
  INDEX idx_created_at (created_at)
) COMMENT='用户基础信息表';

-- 2. 待办事项模板表
CREATE TABLE todo_templates (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
  title VARCHAR(200) NOT NULL COMMENT '待办标题',
  title_en VARCHAR(200) DEFAULT NULL COMMENT '英文标题',
  description TEXT COMMENT '待办描述',
  description_en TEXT COMMENT '英文描述',
  category VARCHAR(50) NOT NULL COMMENT '分类（如：学籍、住宿、财务等）',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
  estimated_duration INT DEFAULT NULL COMMENT '预计完成时间（分钟）',
  required_files JSON DEFAULT NULL COMMENT '需要的文件类型',
  external_url VARCHAR(500) DEFAULT NULL COMMENT '外部链接',
  instructions TEXT COMMENT '操作说明',
  instructions_en TEXT COMMENT '英文操作说明',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_by INT NOT NULL COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (created_by) REFERENCES users(id),
  INDEX idx_category (category),
  INDEX idx_priority (priority),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
) COMMENT='待办事项模板表';

-- 3. 用户待办事项表
CREATE TABLE user_todos (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户待办ID',
  user_id INT NOT NULL COMMENT '用户ID',
  template_id INT NOT NULL COMMENT '模板ID',
  status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
  progress INT DEFAULT 0 COMMENT '进度百分比（0-100）',
  notes TEXT COMMENT '用户备注',
  submitted_files JSON DEFAULT NULL COMMENT '已提交文件信息',
  admin_feedback TEXT COMMENT '管理员反馈',
  started_at TIMESTAMP NULL COMMENT '开始时间',
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  due_date TIMESTAMP NULL COMMENT '截止时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES todo_templates(id),
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_status (status),
  INDEX idx_due_date (due_date),
  INDEX idx_created_at (created_at),
  UNIQUE KEY uk_user_template (user_id, template_id)
) COMMENT='用户待办事项表';

-- 4. 文件上传表
CREATE TABLE uploaded_files (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
  user_todo_id INT NOT NULL COMMENT '用户待办ID',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
  file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  upload_ip VARCHAR(45) COMMENT '上传IP',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
  verified_by INT DEFAULT NULL COMMENT '验证者ID',
  verified_at TIMESTAMP NULL COMMENT '验证时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (user_todo_id) REFERENCES user_todos(id) ON DELETE CASCADE,
  FOREIGN KEY (verified_by) REFERENCES users(id),
  INDEX idx_user_todo_id (user_todo_id),
  INDEX idx_file_type (file_type),
  INDEX idx_is_verified (is_verified),
  INDEX idx_created_at (created_at)
) COMMENT='文件上传记录表';

-- 5. 操作日志表
CREATE TABLE operation_logs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  user_id INT NOT NULL COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  target_type VARCHAR(50) NOT NULL COMMENT '目标类型（user_todo, template等）',
  target_id INT NOT NULL COMMENT '目标ID',
  old_data JSON DEFAULT NULL COMMENT '操作前数据',
  new_data JSON DEFAULT NULL COMMENT '操作后数据',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (user_id) REFERENCES users(id),
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_target (target_type, target_id),
  INDEX idx_created_at (created_at)
) COMMENT='操作日志表';

-- 6. 系统配置表
CREATE TABLE system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  setting_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
  setting_value TEXT COMMENT '配置值',
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '值类型',
  description VARCHAR(255) COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（前端可访问）',
  updated_by INT NOT NULL COMMENT '更新者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (updated_by) REFERENCES users(id),
  INDEX idx_setting_key (setting_key),
  INDEX idx_is_public (is_public)
) COMMENT='系统配置表';
