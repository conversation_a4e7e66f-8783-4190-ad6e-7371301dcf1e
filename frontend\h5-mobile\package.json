{"name": "student-enrollment-h5", "version": "1.0.0", "description": "学生入学待办事项H5移动端", "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts", "lint:fix": "eslint src --ext .vue,.js,.ts --fix"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "vant": "^4.8.0", "axios": "^1.6.0", "vue-i18n": "^9.8.0", "dayjs": "^1.11.0", "@vant/touch-emulator": "^1.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.0", "sass": "^1.69.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0"}}