<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/store/auth'

const authStore = useAuthStore()

onMounted(() => {
  // 应用启动时检查登录状态
  authStore.checkLoginStatus()
})
</script>

<style lang="scss">
#app {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
}

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 自定义Vant主题色
:root {
  --van-primary-color: #1976d2;
  --van-success-color: #4caf50;
  --van-warning-color: #ff9800;
  --van-danger-color: #f44336;
}
</style>
