import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import i18n from './locales'

// Vant UI组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// 触摸模拟器（开发环境）
import '@vant/touch-emulator'

// 全局样式
import './styles/index.scss'

import App from './App.vue'

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Vant)

// 挂载应用
app.mount('#app')
