# 入学待办事项系统 - H5移动端 + 网页管理后台方案

## 📋 项目概述

将现有的微信小程序架构重构为：

- **H5移动端**：学生使用的移动网页应用
- **网页管理后台**：管理员使用的桌面端管理系统
- **后端API**：保持现有架构，适当调整

## 🏗️ 技术架构

### 前端技术栈（方案C：技术先进）

- **H5移动端**：Vue 3 + Vant UI (移动端组件库) + PWA
- **管理后台**：Vue 3 + Element Plus (桌面端组件库)
- **共同技术栈**：
  - Vue Router (路由管理)
  - Pinia (状态管理)
  - Axios (HTTP请求)
  - Vue I18n (国际化)
  - Day.js (时间处理，UTC+8)
- **构建工具**：Vite (快速构建)
- **响应式设计**：支持手机、平板、桌面端
- **PWA支持**：可安装到手机桌面，离线缓存

### 后端技术栈

- **核心框架**：FastAPI (Python 3.8+)
- **数据库**：MySQL 8.0+
- **数据库连接**：aiomysql (异步) + SQLAlchemy (ORM)
- **认证授权**：JWT + python-jose
- **API文档**：自动生成 Swagger/OpenAPI
- **异步支持**：全异步处理，高性能
- **数据库架构**：
  - 主数据库：school_enrollment_todo（待办系统）
  - 学生信息库：smartcampus.stuinfosp（**********）
- **新增功能**：学号验证、双数据库连接、国际化支持、时区处理(UTC+8)
- **依赖管理**：Poetry 或 pip + requirements.txt

### FastAPI 项目依赖

```txt
# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
aiomysql==0.2.0
sqlalchemy[asyncio]==2.0.23
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0
passlib[bcrypt]==1.7.4
```

### 精简项目结构

```
NewStudentEnrollment/                    # 项目根目录
├── README.md                           # 项目说明文档
├── .gitignore                          # Git忽略文件
├── docs/                               # 项目文档
│   ├── api.md                         # API文档
│   └── database.md                    # 数据库文档
│
├── backend/                            # FastAPI后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                    # FastAPI应用入口
│   │   ├── config.py                  # 配置文件
│   │   ├── database.py                # 数据库连接管理
│   │   ├── models/                    # SQLAlchemy模型
│   │   │   ├── __init__.py
│   │   │   ├── base.py               # 基础模型
│   │   │   ├── user.py               # 用户模型
│   │   │   ├── todo.py               # 待办事项模型
│   │   │   └── admin.py              # 管理员模型
│   │   ├── schemas/                   # Pydantic模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py               # 用户数据模型
│   │   │   ├── todo.py               # 待办事项数据模型
│   │   │   ├── auth.py               # 认证数据模型
│   │   │   └── common.py             # 通用数据模型
│   │   ├── api/                       # API路由
│   │   │   ├── __init__.py
│   │   │   ├── auth.py               # 认证相关API
│   │   │   ├── users.py              # 用户管理API
│   │   │   ├── todos.py              # 待办事项API
│   │   │   ├── admin.py              # 管理员API
│   │   │   └── upload.py             # 文件上传API
│   │   ├── core/                      # 核心功能
│   │   │   ├── __init__.py
│   │   │   ├── security.py           # JWT认证和安全
│   │   │   ├── deps.py               # 依赖注入
│   │   │   └── middleware.py         # 中间件
│   │   ├── utils/                     # 工具函数
│   │   │   ├── __init__.py
│   │   │   ├── time_utils.py         # 时间处理工具
│   │   │   ├── file_utils.py         # 文件处理工具
│   │   │   └── i18n_utils.py         # 国际化工具
│   │   └── tests/                     # 测试文件
│   │       ├── __init__.py
│   │       ├── test_auth.py
│   │       ├── test_users.py
│   │       └── test_todos.py
│   ├── requirements.txt               # Python依赖
│   ├── .env                          # 环境变量
│   ├── .env.example                  # 环境变量示例
│   └── README.md                     # 后端说明
│
├── frontend/                          # 前端项目
│   ├── h5-mobile/                    # H5移动端
│   │   ├── public/
│   │   │   ├── index.html
│   │   │   ├── favicon.ico
│   │   │   ├── manifest.json         # PWA配置
│   │   │   └── icons/               # PWA图标
│   │   ├── src/
│   │   │   ├── main.js              # 应用入口
│   │   │   ├── App.vue              # 根组件
│   │   │   ├── router/              # 路由配置
│   │   │   │   └── index.js
│   │   │   ├── store/               # Pinia状态管理
│   │   │   │   ├── index.js
│   │   │   │   ├── auth.js          # 认证状态
│   │   │   │   ├── user.js          # 用户状态
│   │   │   │   └── todo.js          # 待办状态
│   │   │   ├── views/               # 页面组件
│   │   │   │   ├── Login.vue        # 登录页
│   │   │   │   ├── Home.vue         # 主页
│   │   │   │   └── TodoDetail.vue   # 待办详情
│   │   │   ├── components/          # 通用组件
│   │   │   │   ├── Header.vue       # 头部组件
│   │   │   │   ├── ProgressCard.vue # 进度卡片
│   │   │   │   ├── TodoList.vue     # 待办列表
│   │   │   │   └── LanguageSwitch.vue # 语言切换
│   │   │   ├── api/                 # API接口
│   │   │   │   ├── index.js         # API配置
│   │   │   │   ├── auth.js          # 认证API
│   │   │   │   ├── user.js          # 用户API
│   │   │   │   └── todo.js          # 待办API
│   │   │   ├── utils/               # 工具函数
│   │   │   │   ├── request.js       # HTTP请求封装
│   │   │   │   ├── auth.js          # 认证工具
│   │   │   │   ├── time.js          # 时间处理
│   │   │   │   └── storage.js       # 本地存储
│   │   │   ├── locales/             # 国际化文件
│   │   │   │   ├── index.js
│   │   │   │   ├── zh-cn.js         # 中文
│   │   │   │   └── en-us.js         # 英文
│   │   │   ├── styles/              # 样式文件
│   │   │   │   ├── index.scss       # 全局样式
│   │   │   │   ├── variables.scss   # 变量
│   │   │   │   └── mixins.scss      # 混入
│   │   │   └── assets/              # 静态资源
│   │   │       ├── images/
│   │   │       └── icons/
│   │   ├── package.json             # 依赖配置
│   │   ├── vite.config.js           # Vite配置
│   │   ├── .env                     # 环境变量
│   │   ├── .env.production          # 生产环境变量
│   │   └── README.md                # H5端说明
│   │
│   └── admin-web/                   # 管理后台
│       ├── public/
│       │   ├── index.html
│       │   └── favicon.ico
│       ├── src/
│       │   ├── main.js              # 应用入口
│       │   ├── App.vue              # 根组件
│       │   ├── router/              # 路由配置
│       │   │   └── index.js
│       │   ├── store/               # Pinia状态管理
│       │   │   ├── index.js
│       │   │   ├── auth.js          # 认证状态
│       │   │   ├── user.js          # 用户管理状态
│       │   │   └── system.js        # 系统状态
│       │   ├── views/               # 页面组件
│       │   │   ├── Login.vue        # 登录页
│       │   │   ├── Dashboard.vue    # 仪表板
│       │   │   ├── UserManagement.vue # 用户管理
│       │   │   ├── TodoManagement.vue # 待办管理
│       │   │   └── SystemSettings.vue # 系统设置
│       │   ├── components/          # 通用组件
│       │   │   ├── Layout/          # 布局组件
│       │   │   │   ├── Header.vue
│       │   │   │   ├── Sidebar.vue
│       │   │   │   └── Footer.vue
│       │   │   ├── Charts/          # 图表组件
│       │   │   │   ├── ProgressChart.vue
│       │   │   │   └── StatisticsChart.vue
│       │   │   └── Common/          # 通用组件
│       │   │       ├── DataTable.vue
│       │   │       └── SearchForm.vue
│       │   ├── api/                 # API接口
│       │   │   ├── index.js
│       │   │   ├── auth.js
│       │   │   ├── user.js
│       │   │   ├── todo.js
│       │   │   └── system.js
│       │   ├── utils/               # 工具函数
│       │   │   ├── request.js
│       │   │   ├── auth.js
│       │   │   ├── permission.js
│       │   │   └── export.js
│       │   ├── locales/             # 国际化文件
│       │   │   ├── index.js
│       │   │   ├── zh-cn.js
│       │   │   └── en-us.js
│       │   ├── styles/              # 样式文件
│       │   │   ├── index.scss
│       │   │   ├── variables.scss
│       │   │   └── element-plus.scss
│       │   └── assets/              # 静态资源
│       │       ├── images/
│       │       └── icons/
│       ├── package.json             # 依赖配置
│       ├── vite.config.js           # Vite配置
│       ├── .env                     # 环境变量
│       ├── .env.production          # 生产环境变量
│       └── README.md                # 管理端说明
│
└── database/                        # 数据库相关
    ├── schema.sql                   # 数据库结构
    ├── init_data.sql               # 初始数据
    └── README.md                    # 数据库说明
```

## 📱 H5移动端功能设计

### 核心页面（简化设计）

1. **登录页面**

   - 学号登录（无密码验证，简化流程）
   - 学号格式验证（基础格式检查）
   - 与smartcampus数据库验证学号有效性
   - 记住登录状态
   - 语言切换（中/英文）
   - 测试登录（开发环境）
2. **主页/待办列表页面**

   - 顶部：欢迎信息（显示学生姓名stucName）+ 语言切换按钮
   - 进度环形图
   - 统计数据（总计/已完成/待处理）
   - 待办事项列表
   - 直接点击进入详情
3. **待办详情页面**

   - 详细信息展示
   - 个人进度统计（移至此处）
   - 状态更新
   - 文件上传
   - 操作历史
   - 相关链接
   - 语言切换按钮

### 用户体验特性

- **响应式设计**：适配各种屏幕尺寸
- **触摸友好**：按钮大小符合移动端标准
- **加载动画**：提供良好的加载反馈
- **错误处理**：友好的错误提示
- **离线支持**：基础功能离线可用
- **安装提示**：支持添加到主屏幕
- **国际化支持**：中英文切换，界面文本完全本地化
- **时区统一**：所有时间显示均为北京时间(UTC+8)
- **真实数据**：直接使用学校学生信息系统数据
- **简化导航**：专注核心功能，减少不必要页面

## 💻 网页管理后台功能设计

### 核心模块

1. **登录系统**

   - 管理员账号密码登录
   - 权限验证
   - 会话管理
2. **仪表板**

   - 数据概览
   - 图表统计
   - 快捷操作
   - 系统状态
3. **学生管理**

   - 学生列表
   - 学生信息编辑
   - 批量导入
   - 状态管理
4. **待办模板管理**

   - 模板创建/编辑
   - 模板分类
   - 模板启用/禁用
   - 批量操作
5. **进度监控**

   - 整体进度统计
   - 个人进度查看
   - 异常情况提醒
   - 数据导出
6. **系统管理**

   - 用户权限管理
   - 系统配置
   - 日志查看
   - 数据备份

### 管理功能特性

- **权限控制**：基于角色的访问控制
- **数据可视化**：图表展示统计数据
- **批量操作**：支持批量处理
- **数据导出**：Excel/CSV格式导出
- **操作日志**：记录所有操作
- **响应式布局**：支持平板和桌面端

## 🔄 数据流设计

### 用户认证流程

1. **学生端**：用户输入学号
2. 前端验证学号格式（基础格式检查）
3. 发送学号到后端验证（检查学号是否存在）
4. 后端返回JWT token和用户信息
5. 前端存储token到localStorage
6. 后续请求携带token

### 管理员认证流程

1. **管理端**：输入管理员账号密码
2. 前端验证基本格式
3. 发送到后端验证
4. 后端返回JWT token和管理员信息
5. 前端存储token到localStorage
6. 后续请求携带token

### 数据同步机制

- **实时更新**：关键数据实时同步
- **缓存策略**：非关键数据本地缓存
- **离线支持**：基础功能离线可用
- **冲突解决**：在线时同步离线操作
- **时区处理**：所有时间统一转换为北京时间(UTC+8)显示
- **国际化数据**：根据用户语言偏好返回对应文本

## 🎨 UI/UX设计原则

### 移动端设计

- **Material Design**：遵循Google Material Design规范
- **触摸优先**：按钮大小不小于44px
- **简洁明了**：减少认知负担
- **快速响应**：操作反馈及时

### 管理后台设计

- **专业简洁**：适合长时间使用
- **信息密度**：合理的信息展示密度
- **操作效率**：支持键盘快捷键
- **数据可视化**：直观的图表展示

## 🚀 开发阶段规划

### 第一阶段：项目初始化和基础框架（1-2周）

- [X] **项目结构搭建**
  - [X] 创建完整项目目录结构
  - [X] 初始化Git仓库和.gitignore
  - [X] 创建项目文档结构
- [ ] **后端FastAPI环境搭建**
  - [ ] 创建backend目录结构
  - [ ] 安装Python依赖（requirements.txt）
  - [ ] 配置环境变量（.env文件）
  - [ ] 初始化FastAPI应用（main.py）
  - [ ] 配置双数据库连接（database.py）
  - [ ] 设置JWT认证中间件
  - [ ] 配置CORS和API文档
- [ ] **前端项目初始化**
  - [ ] 创建H5移动端项目（Vue 3 + Vant UI + Vite）
  - [ ] 创建管理后台项目（Vue 3 + Element Plus + Vite）
  - [ ] 配置项目依赖（package.json）
  - [ ] 设置Vite构建配置
  - [ ] 配置代理和环境变量
- [ ] **通用配置**
  - [ ] 国际化配置（Vue I18n）
  - [ ] 时区处理工具（Day.js UTC+8）
  - [ ] API请求封装（axios）
  - [ ] 状态管理配置（Pinia）

### 第二阶段：核心功能开发（2-3周）

- [ ] **后端API开发**
  - [ ] 数据库模型定义（SQLAlchemy Models）
  - [ ] 数据验证模型（Pydantic Schemas）
  - [ ] 认证API路由（auth.py）
    - [ ] 学号登录接口（整合smartcampus数据库）
    - [ ] 管理员登录接口
    - [ ] JWT token验证和刷新
  - [ ] 用户管理API（users.py）
  - [ ] 待办事项API（todos.py）
  - [ ] 文件上传API（upload.py）
  - [ ] 管理员API（admin.py）
- [ ] **前端页面开发**
  - [ ] **H5移动端页面**
    - [ ] 登录页面（Login.vue）
    - [ ] 主页/待办列表（Home.vue）
    - [ ] 待办详情页（TodoDetail.vue）
    - [ ] 通用组件（Header, ProgressCard, TodoList等）
  - [ ] **管理后台页面**
    - [ ] 登录页面（Login.vue）
    - [ ] 仪表板（Dashboard.vue）
    - [ ] 用户管理（UserManagement.vue）
    - [ ] 待办管理（TodoManagement.vue）
    - [ ] 系统设置（SystemSettings.vue）
- [ ] **数据集成**
  - [ ] 学生信息同步机制
  - [ ] 前后端API对接
  - [ ] 数据统计和图表
- [ ] **国际化和时区**
  - [ ] 前端国际化文件（zh-cn.js, en-us.js）
  - [ ] API响应国际化支持
  - [ ] 时间显示统一（北京时间）

### 第三阶段：高级功能和优化（2-3周）

- [ ] **权限管理系统**
  - [ ] 基于角色的访问控制（RBAC）
  - [ ] 权限中间件和装饰器
  - [ ] 前端路由权限控制
- [ ] **数据可视化**
  - [ ] 统计图表组件（ECharts）
  - [ ] 实时数据更新
  - [ ] 数据导出功能（Excel/CSV）
- [ ] **PWA功能**
  - [ ] Service Worker配置
  - [ ] 离线缓存策略
  - [ ] 安装提示和图标
- [ ] **性能优化**
  - [ ] 前端代码分割和懒加载
  - [ ] 后端查询优化和缓存
  - [ ] 图片压缩和CDN
  - [ ] 数据库索引优化

### 第四阶段：测试和部署（1-2周）

- [ ] **功能测试**
  - [ ] 后端API测试（Postman/自动化测试）
  - [ ] 前端功能测试（手动测试）
  - [ ] 前后端联调测试
  - [ ] 兼容性测试（多浏览器、多设备）
- [ ] **部署准备**
  - [ ] 生产环境配置
  - [ ] 数据库初始化脚本
  - [ ] 前端构建和静态文件部署
  - [ ] 后端服务部署
- [ ] **上线和维护**
  - [ ] 域名配置和访问测试
  - [ ] 数据备份策略
  - [ ] 基础监控和日志
  - [ ] 用户培训和文档

## 📊 技术选型对比

### 前端框架选择（已确定）

| 方案             | 优点                       | 缺点                 | 选择               |
| ---------------- | -------------------------- | -------------------- | ------------------ |
| ~~原生JS~~      | 轻量、无依赖、学习成本低   | 开发效率低、维护困难 | ❌                 |
| **Vue.js** | 易学易用、生态丰富、组件化 | 需要构建工具         | ✅**已选择** |
| ~~React~~       | 生态最丰富、性能好         | 学习曲线陡峭         | ❌                 |

**确定技术栈：**

- H5移动端：Vue 3 + Vant UI + PWA
- 管理后台：Vue 3 + Element Plus
- 国际化：Vue I18n
- 时间处理：Day.js (UTC+8)

### 部署方案

| 方案     | 适用场景             | 成本 | 推荐度     |
| -------- | -------------------- | ---- | ---------- |
| 云服务器 | 完全控制、自定义配置 | 中等 | ⭐⭐⭐⭐   |
| 静态托管 | 前端部署、CDN加速    | 低   | ⭐⭐⭐⭐⭐ |
| 容器化   | 微服务、易扩展       | 高   | ⭐⭐⭐     |

## 🗄️ 数据库整合方案

### 现有学生信息数据库

- **数据库地址**：**********
- **数据库名**：smartcampus
- **表名**：stuinfosp
- **用户名**：ethanchen
- **密码**：Spal#2@25
- **关键字段**：
  - `stucName`：学生姓名
  - `stuid`：学生学号

### 双数据库架构

```python
# FastAPI 后端数据库配置
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import aiomysql
from typing import Optional

# 数据库配置
DATABASE_CONFIGS = {
    # 学生信息数据库（只读）
    "student_db": {
        "host": "**********",
        "user": "ethanchen",
        "password": "Spal#2@25",
        "database": "smartcampus",
        "charset": "utf8mb4"
    },

    # 待办系统数据库（读写）
    "todo_system_db": {
        "host": "localhost",
        "user": "root",
        "password": "your_password",
        "database": "school_enrollment_todo",
        "charset": "utf8mb4"
    }
}

# SQLAlchemy 异步引擎
STUDENT_DB_URL = f"mysql+aiomysql://{DATABASE_CONFIGS['student_db']['user']}:{DATABASE_CONFIGS['student_db']['password']}@{DATABASE_CONFIGS['student_db']['host']}/{DATABASE_CONFIGS['student_db']['database']}"

TODO_DB_URL = f"mysql+aiomysql://{DATABASE_CONFIGS['todo_system_db']['user']}:{DATABASE_CONFIGS['todo_system_db']['password']}@{DATABASE_CONFIGS['todo_system_db']['host']}/{DATABASE_CONFIGS['todo_system_db']['database']}"
```

### 数据库连接管理

```python
# FastAPI 数据库管理器
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import aiomysql
from typing import Optional, Dict, Any

class DatabaseManager:
    def __init__(self):
        # 创建异步引擎
        self.student_engine = create_async_engine(STUDENT_DB_URL, echo=True)
        self.todo_engine = create_async_engine(TODO_DB_URL, echo=True)

        # 创建会话工厂
        self.StudentSession = sessionmaker(
            self.student_engine, class_=AsyncSession, expire_on_commit=False
        )
        self.TodoSession = sessionmaker(
            self.todo_engine, class_=AsyncSession, expire_on_commit=False
        )

    async def get_student_info(self, student_id: str) -> Optional[Dict[str, Any]]:
        """查询学生信息"""
        async with self.StudentSession() as session:
            result = await session.execute(
                "SELECT stucName, stuid FROM stuinfosp WHERE stuid = :student_id",
                {"student_id": student_id}
            )
            row = result.fetchone()
            if row:
                return {"stucName": row[0], "stuid": row[1]}
            return None

    async def create_or_update_local_user(self, student_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建或更新本地用户"""
        async with self.TodoSession() as session:
            # 检查用户是否存在
            result = await session.execute(
                "SELECT * FROM users WHERE student_id = :student_id",
                {"student_id": student_info["stuid"]}
            )
            existing_user = result.fetchone()

            if not existing_user:
                # 创建新用户
                await session.execute(
                    """INSERT INTO users (student_id, real_name, nickname, role, created_at)
                       VALUES (:student_id, :real_name, :nickname, :role, NOW())""",
                    {
                        "student_id": student_info["stuid"],
                        "real_name": student_info["stucName"],
                        "nickname": student_info["stucName"],
                        "role": "student"
                    }
                )
            else:
                # 更新用户信息
                await session.execute(
                    """UPDATE users SET real_name = :real_name, nickname = :nickname,
                       last_sync_time = NOW() WHERE student_id = :student_id""",
                    {
                        "real_name": student_info["stucName"],
                        "nickname": student_info["stucName"],
                        "student_id": student_info["stuid"]
                    }
                )

            await session.commit()

            # 返回用户信息
            result = await session.execute(
                "SELECT * FROM users WHERE student_id = :student_id",
                {"student_id": student_info["stuid"]}
            )
            user = result.fetchone()
            return dict(user._mapping) if user else None

# 全局数据库管理器实例
db_manager = DatabaseManager()
```

### FastAPI 应用配置

```python
# app/main.py - FastAPI应用入口
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, users, todos
from app.database import db_manager

# 创建FastAPI应用
app = FastAPI(
    title="入学待办事项系统API",
    description="学生入学待办事项管理系统后端API",
    version="1.0.0",
    docs_url="/docs",  # Swagger UI
    redoc_url="/redoc"  # ReDoc
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/users", tags=["用户管理"])
app.include_router(todos.router, prefix="/api/todos", tags=["待办事项"])

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    print("FastAPI应用启动成功")
    print("API文档地址: http://localhost:8000/docs")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    print("FastAPI应用关闭")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "入学待办事项系统API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}
```

### 环境配置

```python
# app/config.py - 配置管理
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 数据库配置
    STUDENT_DB_HOST: str = "**********"
    STUDENT_DB_USER: str = "ethanchen"
    STUDENT_DB_PASSWORD: str = "Spal#2@25"
    STUDENT_DB_NAME: str = "smartcampus"

    TODO_DB_HOST: str = "localhost"
    TODO_DB_USER: str = "root"
    TODO_DB_PASSWORD: str = "your_password"
    TODO_DB_NAME: str = "school_enrollment_todo"

    # JWT配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 应用配置
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    class Config:
        env_file = ".env"

settings = Settings()
```

### .env 环境变量文件

```bash
# .env - 环境变量配置
# 数据库配置
STUDENT_DB_HOST=**********
STUDENT_DB_USER=ethanchen
STUDENT_DB_PASSWORD=Spal#2@25
STUDENT_DB_NAME=smartcampus

TODO_DB_HOST=localhost
TODO_DB_USER=root
TODO_DB_PASSWORD=your_password
TODO_DB_NAME=school_enrollment_todo

# JWT配置
SECRET_KEY=your-very-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
DEBUG=True
HOST=0.0.0.0
PORT=8000
```

### 前端项目配置示例

#### H5移动端 package.json

```json
{
  "name": "student-enrollment-h5",
  "version": "1.0.0",
  "description": "学生入学待办事项H5移动端",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .vue,.js,.ts",
    "lint:fix": "eslint src --ext .vue,.js,.ts --fix"
  },
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "vant": "^4.8.0",
    "axios": "^1.6.0",
    "vue-i18n": "^9.8.0",
    "dayjs": "^1.11.0",
    "@vant/touch-emulator": "^1.4.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "vite": "^5.0.0",
    "vite-plugin-pwa": "^0.17.0",
    "sass": "^1.69.0",
    "eslint": "^8.55.0",
    "eslint-plugin-vue": "^9.19.0"
  }
}
```

#### 管理后台 package.json

```json
{
  "name": "student-enrollment-admin",
  "version": "1.0.0",
  "description": "学生入学待办事项管理后台",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .vue,.js,.ts",
    "lint:fix": "eslint src --ext .vue,.js,.ts --fix"
  },
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0",
    "vue-i18n": "^9.8.0",
    "dayjs": "^1.11.0",
    "echarts": "^5.4.0",
    "vue-echarts": "^6.6.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "vite": "^5.0.0",
    "sass": "^1.69.0",
    "eslint": "^8.55.0",
    "eslint-plugin-vue": "^9.19.0",
    "unplugin-auto-import": "^0.17.0",
    "unplugin-vue-components": "^0.26.0"
  }
}
```

#### Vite 配置示例 (vite.config.js)

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import path from 'path'

export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      manifest: {
        name: '学生入学待办事项',
        short_name: '入学待办',
        description: '学生入学待办事项管理系统',
        theme_color: '#1976d2',
        background_color: '#ffffff',
        display: 'standalone',
        icons: [
          {
            src: 'icons/icon-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icons/icon-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['vant'] // 或 ['element-plus'] 对于管理后台
        }
      }
    }
  }
})
```

## 🗄️ 本地数据库详细设计

### 数据库名称：school_enrollment_todo

### 核心数据表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
  real_name VARCHAR(100) NOT NULL COMMENT '真实姓名（来自smartcampus）',
  nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  role ENUM('student', 'admin') DEFAULT 'student' COMMENT '用户角色',
  language VARCHAR(10) DEFAULT 'zh-cn' COMMENT '语言偏好',
  avatar_url VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
  last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后同步时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_student_id (student_id),
  INDEX idx_role (role),
  INDEX idx_created_at (created_at)
) COMMENT='用户基础信息表';
```

#### 2. 待办事项模板表 (todo_templates)
```sql
CREATE TABLE todo_templates (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
  title VARCHAR(200) NOT NULL COMMENT '待办标题',
  title_en VARCHAR(200) DEFAULT NULL COMMENT '英文标题',
  description TEXT COMMENT '待办描述',
  description_en TEXT COMMENT '英文描述',
  category VARCHAR(50) NOT NULL COMMENT '分类（如：学籍、住宿、财务等）',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
  estimated_duration INT DEFAULT NULL COMMENT '预计完成时间（分钟）',
  required_files JSON DEFAULT NULL COMMENT '需要的文件类型',
  external_url VARCHAR(500) DEFAULT NULL COMMENT '外部链接',
  instructions TEXT COMMENT '操作说明',
  instructions_en TEXT COMMENT '英文操作说明',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_by INT NOT NULL COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (created_by) REFERENCES users(id),
  INDEX idx_category (category),
  INDEX idx_priority (priority),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
) COMMENT='待办事项模板表';
```

#### 3. 用户待办事项表 (user_todos)
```sql
CREATE TABLE user_todos (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户待办ID',
  user_id INT NOT NULL COMMENT '用户ID',
  template_id INT NOT NULL COMMENT '模板ID',
  status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
  progress INT DEFAULT 0 COMMENT '进度百分比（0-100）',
  notes TEXT COMMENT '用户备注',
  submitted_files JSON DEFAULT NULL COMMENT '已提交文件信息',
  admin_feedback TEXT COMMENT '管理员反馈',
  started_at TIMESTAMP NULL COMMENT '开始时间',
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  due_date TIMESTAMP NULL COMMENT '截止时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES todo_templates(id),
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_status (status),
  INDEX idx_due_date (due_date),
  INDEX idx_created_at (created_at),
  UNIQUE KEY uk_user_template (user_id, template_id)
) COMMENT='用户待办事项表';
```

#### 4. 文件上传表 (uploaded_files)
```sql
CREATE TABLE uploaded_files (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
  user_todo_id INT NOT NULL COMMENT '用户待办ID',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
  file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  upload_ip VARCHAR(45) COMMENT '上传IP',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
  verified_by INT DEFAULT NULL COMMENT '验证者ID',
  verified_at TIMESTAMP NULL COMMENT '验证时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (user_todo_id) REFERENCES user_todos(id) ON DELETE CASCADE,
  FOREIGN KEY (verified_by) REFERENCES users(id),
  INDEX idx_user_todo_id (user_todo_id),
  INDEX idx_file_type (file_type),
  INDEX idx_is_verified (is_verified),
  INDEX idx_created_at (created_at)
) COMMENT='文件上传记录表';
```

#### 5. 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  user_id INT NOT NULL COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  target_type VARCHAR(50) NOT NULL COMMENT '目标类型（user_todo, template等）',
  target_id INT NOT NULL COMMENT '目标ID',
  old_data JSON DEFAULT NULL COMMENT '操作前数据',
  new_data JSON DEFAULT NULL COMMENT '操作后数据',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (user_id) REFERENCES users(id),
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_target (target_type, target_id),
  INDEX idx_created_at (created_at)
) COMMENT='操作日志表';
```

#### 6. 系统配置表 (system_settings)
```sql
CREATE TABLE system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  setting_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
  setting_value TEXT COMMENT '配置值',
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '值类型',
  description VARCHAR(255) COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（前端可访问）',
  updated_by INT NOT NULL COMMENT '更新者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (updated_by) REFERENCES users(id),
  INDEX idx_setting_key (setting_key),
  INDEX idx_is_public (is_public)
) COMMENT='系统配置表';
```

### 表关系说明

1. **users** ← **user_todos** ← **uploaded_files**
   - 用户可以有多个待办事项，每个待办事项可以有多个文件

2. **todo_templates** ← **user_todos**
   - 模板可以被多个用户使用，生成多个用户待办事项

3. **users** ← **operation_logs**
   - 记录用户的所有操作历史

4. **users** ← **system_settings**
   - 记录系统配置的更新者

### 初始数据示例

```sql
-- 插入管理员用户
INSERT INTO users (student_id, real_name, nickname, role, language) VALUES
('admin', '系统管理员', '管理员', 'admin', 'zh-cn');

-- 插入基础待办模板
INSERT INTO todo_templates (title, title_en, description, description_en, category, priority, created_by) VALUES
('学籍注册', 'Student Registration', '完成学籍信息注册和确认', 'Complete student registration and confirmation', '学籍', 'high', 1),
('宿舍申请', 'Dormitory Application', '申请学生宿舍住宿', 'Apply for student dormitory accommodation', '住宿', 'medium', 1),
('学费缴纳', 'Tuition Payment', '缴纳学期学费', 'Pay semester tuition fees', '财务', 'high', 1);

-- 插入系统配置
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public, updated_by) VALUES
('system_name', '入学待办事项系统', 'string', '系统名称', TRUE, 1),
('default_language', 'zh-cn', 'string', '默认语言', TRUE, 1),
('file_upload_max_size', '10485760', 'number', '文件上传最大大小（字节）', FALSE, 1);
```

## 🔑 登录方式详细设计

### 学生端登录（整合smartcampus数据库）

```python
# FastAPI 登录路由和验证逻辑
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional
import jwt
from datetime import datetime, timedelta

app = FastAPI(title="入学待办事项系统API", version="1.0.0")

# 请求模型
class StudentLoginRequest(BaseModel):
    studentId: str
    language: str = "zh-cn"

class AdminLoginRequest(BaseModel):
    username: str
    password: str
    language: str = "zh-cn"

# JWT 配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@app.post("/api/auth/student-login")
async def student_login(request: StudentLoginRequest):
    """
    学生登录流程：
    1. 用户输入学号（如：2024001001）
    2. 前端基础验证：非空检查、格式检查（数字，长度等）
    3. 后端验证流程：
       a) 查询 smartcampus.stuinfosp 表验证学号
       b) 获取学生真实姓名 (stucName)
       c) 检查/创建本地用户记录
       d) 生成JWT token
    4. 返回结果包含token和用户信息
    """
    try:
        # 1. 从 smartcampus 查询学生信息
        student_info = await db_manager.get_student_info(request.studentId)
        if not student_info:
            raise HTTPException(status_code=404, detail="学号不存在或无效")

        # 2. 创建或更新本地用户记录
        local_user = await db_manager.create_or_update_local_user(student_info)
        if not local_user:
            raise HTTPException(status_code=500, detail="用户信息创建失败")

        # 3. 生成JWT token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": str(local_user["id"]),
                "student_id": request.studentId,
                "role": "student",
                "language": request.language
            },
            expires_delta=access_token_expires
        )

        # 4. 返回结果
        return {
            "success": True,
            "token": access_token,
            "user": {
                "id": local_user["id"],
                "studentId": request.studentId,
                "name": student_info["stucName"],
                "realName": student_info["stucName"],
                "language": request.language,
                "role": "student"
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

async def validate_student_login(student_id: str) -> dict:
    """后端验证逻辑"""
    # 1. 从 smartcampus 查询学生信息
    student_info = await db_manager.get_student_info(student_id)
    if not student_info:
        raise HTTPException(status_code=404, detail="学号不存在或无效")

    # 2. 创建或更新本地用户记录
    local_user = await db_manager.create_or_update_local_user(student_info)

    return local_user
```

### 管理员登录（传统方式）

```python
@app.post("/api/auth/admin-login")
async def admin_login(request: AdminLoginRequest):
    """
    管理员登录 - 使用账号密码验证
    POST /api/auth/admin-login
    {
      "username": "admin",
      "password": "password123",
      "language": "zh-cn"
    }
    """
    try:
        # 验证管理员账号密码（这里需要实现具体的验证逻辑）
        if not await verify_admin_credentials(request.username, request.password):
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 生成JWT token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": request.username,
                "role": "admin",
                "language": request.language
            },
            expires_delta=access_token_expires
        )

        return {
            "success": True,
            "token": access_token,
            "user": {
                "username": request.username,
                "role": "admin",
                "language": request.language
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

async def verify_admin_credentials(username: str, password: str) -> bool:
    """验证管理员凭据"""
    # 这里实现具体的管理员验证逻辑
    # 可以从数据库查询或使用固定的管理员账号
    return username == "admin" and password == "password123"
```

### 登录状态管理

- 使用JWT token存储在localStorage
- token包含用户信息和语言偏好
- 自动续期机制
- 退出时清除所有本地数据

## 🌍 国际化(i18n)详细设计

### 支持语言

- **中文简体** (zh-cn) - 默认
- **英文** (en-us)

### 国际化实现

```javascript
// Vue I18n 配置
const messages = {
  'zh-cn': {
    login: {
      title: '登录',
      studentId: '学号',
      placeholder: '请输入学号',
      submit: '登录',
      success: '登录成功'
    },
    todo: {
      title: '待办事项',
      status: {
        pending: '待处理',
        processing: '进行中',
        completed: '已完成'
      }
    }
  },
  'en-us': {
    login: {
      title: 'Login',
      studentId: 'Student ID',
      placeholder: 'Enter your student ID',
      submit: 'Login',
      success: 'Login successful'
    },
    todo: {
      title: 'To-Do Items',
      status: {
        pending: 'Pending',
        processing: 'In Progress',
        completed: 'Completed'
      }
    }
  }
}
```

### 语言切换机制

1. 用户选择语言
2. 保存到localStorage和用户偏好
3. 更新界面文本
4. 同步到后端用户设置
5. 后续API请求返回对应语言的数据

## ⏰ 时区处理详细设计

### 时间统一原则

- **存储**：数据库统一存储UTC时间
- **传输**：API传输ISO 8601格式
- **显示**：前端统一显示北京时间(UTC+8)

### 时间处理实现

```javascript
// Day.js 配置
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

// 工具函数
export const timeUtils = {
  // 格式化显示时间（统一显示北京时间）
  formatDisplay(utcTime) {
    return dayjs(utcTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
  },

  // 相对时间显示
  fromNow(utcTime) {
    return dayjs(utcTime).tz('Asia/Shanghai').fromNow()
  },

  // 获取当前北京时间
  now() {
    return dayjs().tz('Asia/Shanghai')
  }
}
```

### 时间显示格式

- **完整时间**：2024-12-05 14:30:00
- **日期**：2024-12-05
- **相对时间**：2小时前、昨天、3天前
- **国际化时间**：根据语言显示格式

## 🔧 开发工具推荐

### 开发环境

- **代码编辑器**：VS Code
- **版本控制**：Git
- **API测试**：Postman
- **调试工具**：Chrome DevTools

### 构建工具（可选）

- **打包工具**：Webpack/Vite
- **CSS预处理**：Sass/Less
- **代码检查**：ESLint
- **格式化**：Prettier

## 📈 性能优化策略

### 前端优化

- **代码分割**：按需加载
- **图片优化**：WebP格式、懒加载
- **缓存策略**：合理的缓存设置
- **压缩优化**：Gzip压缩

### 后端优化

- **数据库优化**：索引优化、查询优化
- **缓存机制**：Redis缓存
- **API优化**：分页、字段筛选
- **监控告警**：性能监控

## 🛡️ 安全考虑

### 前端安全

- **XSS防护**：输入验证、输出编码
- **CSRF防护**：Token验证
- **敏感信息**：不在前端存储敏感数据

### 后端安全

- **身份验证**：JWT token
- **权限控制**：RBAC模型
- **数据验证**：输入验证、SQL注入防护
- **HTTPS**：全站HTTPS

## 📝 后续扩展计划

### 功能扩展

- **消息推送**：重要事项提醒（支持多语言）
- **更多语言支持**：日语、韩语等
- **主题切换**：深色模式
- **数据分析**：更详细的统计
- **时区扩展**：支持其他时区（如有海外学生）
- **语音播报**：重要通知语音提醒（多语言）

### 技术升级

- **微前端**：模块化开发
- **GraphQL**：更灵活的API
- **TypeScript**：类型安全
- **测试覆盖**：单元测试、集成测试

---

## 📱 简化后的界面设计

### 移动端导航结构

```
登录页面 → 主页/待办列表 → 待办详情
```

### 页面功能分布

1. **登录页面**

   - 学号输入框
   - 语言切换按钮
   - 登录按钮
2. **主页（待办列表）**

   - 顶部栏：欢迎信息（显示真实姓名）+ 语言切换
   - 进度概览卡片
   - 待办事项列表
   - 无底部导航栏（简化设计）
3. **待办详情页面**

   - 返回按钮 + 标题 + 语言切换
   - 待办详细信息
   - 个人进度统计（从个人中心移至此处）
   - 操作按钮

### 路由配置（简化）

```javascript
const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: Login },
  { path: '/home', component: Home, meta: { requiresAuth: true } },
  { path: '/todo/:id', component: TodoDetail, meta: { requiresAuth: true } }
]
```

## 💡 建议和注意事项

1. **渐进式开发**：先实现核心功能，再逐步完善
2. **用户反馈**：及时收集用户反馈，快速迭代
3. **性能监控**：建立性能监控体系
4. **文档维护**：保持文档更新
5. **备份策略**：定期数据备份
6. **国际化测试**：确保中英文切换无问题
7. **时区测试**：验证时间显示的准确性
8. **学号格式**：制定统一的学号格式规范
9. **数据库连接**：确保smartcampus数据库连接稳定性
10. **信息同步**：定期同步学生基础信息

## 🎯 确定方案总结

### ✅ 已确定的技术选型

- **前端架构**：方案C - Vue 3 技术栈
  - H5移动端：Vue 3 + Vant UI + PWA
  - 管理后台：Vue 3 + Element Plus
- **后端架构**：FastAPI + Python 3.8+
  - 异步处理：aiomysql + SQLAlchemy
  - 认证授权：JWT + python-jose
  - API文档：自动生成 Swagger/OpenAPI
- **页面结构**：简化为3个核心页面（登录、主页、详情）
- **登录方式**：学生端学号登录（整合smartcampus数据库），管理端账号密码
- **数据库架构**：双数据库（smartcampus学生信息 + 本地待办系统）
- **国际化**：中英文双语支持，Vue I18n实现
- **时区处理**：统一显示北京时间(UTC+8)，Day.js处理

### 🗄️ 数据库整合要点

- **学生信息源**：smartcampus.stuinfosp (**********)
- **关键字段**：stuid(学号), stucName(姓名)
- **验证机制**：学号登录时实时验证并同步学生信息
- **本地存储**：在待办系统中创建用户记录，关联学号

### 🚀 下一步行动

1. **后端FastAPI环境搭建**
   - 创建FastAPI项目结构
   - 安装依赖（fastapi, uvicorn, aiomysql等）
   - 配置双数据库连接
   - 实现JWT认证中间件
2. **前端Vue 3项目搭建**
   - 创建H5移动端项目（Vue 3 + Vant UI）
   - 创建管理后台项目（Vue 3 + Element Plus）
   - 配置国际化和时区处理
3. **核心功能实现**
   - 实现学号登录和验证功能
   - 创建简化的页面结构
   - 连接前后端API

### 🛠️ 开发环境启动命令

#### 后端FastAPI启动
```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 访问API文档
# http://localhost:8000/docs (Swagger UI)
# http://localhost:8000/redoc (ReDoc)
```

#### 前端项目启动
```bash
# H5移动端
cd frontend/h5-mobile
npm install
npm run dev
# 访问: http://localhost:3000

# 管理后台
cd frontend/admin-web
npm install
npm run dev
# 访问: http://localhost:3001
```

#### 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE school_enrollment_todo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p school_enrollment_todo < database/schema.sql

# 导入初始数据
mysql -u root -p school_enrollment_todo < database/init_data.sql
```

**方案已确定，准备开始实施！** 🎉
